from nicegui import ui

# Add CSS to prevent scrolling on mobile
ui.add_head_html("""
<style>
    body, html {
        overflow: hidden !important;
        touch-action: none !important;
        position: fixed !important;
        width: 100% !important;
        height: 100% !important;
    }

    /* Prevent pull-to-refresh and overscroll */
    body {
        overscroll-behavior: none !important;
    }
</style>
""")


def handle_button_action(action_name: str) -> None:
    """Handle button actions - replace with actual functionality."""
    ui.notify(f"{action_name} activated!", type="info")


fullscreen = ui.fullscreen()

with ui.element("div").classes("flex w-full h-screen overflow-hidden touch-none"):
    # Sidebar
    with ui.element("div").classes("w-[10%] max-w-xs bg-base-200 p-4"):
        # Top icon
        with ui.element("div").classes("w-full flex justify-center"):
            ui.button(icon="settings", on_click=fullscreen.toggle).classes(
                "text-white bg-transparent w-full h-auto aspect-square"
            )

    # Main
    with ui.element("div").classes("grow bg-base-100 p-4 flex flex-col overflow-hidden"):
        with ui.grid(rows=5, columns=4).classes("w-full h-full gap-2"):
            for i in range(1, 21):  # 5x4 = 20 buttons
                ui.button(f"Button {i}").classes("w-full h-full min-h-0")


ui.run(
    dark=None,
    title="BoopBoard",
    favicon="🗿",
    viewport="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",
)
