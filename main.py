from nicegui import ui

ui.add_css("""
body, html {
    overflow: hidden !important;
    touch-action: none !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
}

/* Prevent pull-to-refresh and overscroll */
body {
    overscroll-behavior: none !important;
}
""")


def handle_button_action(action_name: str) -> None:
    """Handle button actions - replace with actual functionality."""
    ui.notify(f"{action_name} activated!", type="info")


def handle_layer_change(layer_num: int) -> None:
    """Handle layer changes - replace with actual functionality."""
    ui.notify(f"Switched to Layer {layer_num}", type="positive")


fullscreen = ui.fullscreen()

with ui.element("div").classes("flex w-full h-screen overflow-hidden touch-none"):
    # Layer Sidebar
    with ui.element("div").classes("w-16 bg-base-300 p-2 flex flex-col gap-2"):
        # Toggle fullscreen
        ui.button(
            icon="fullscreen",
            on_click=lambda: fullscreen.toggle(),
        ).classes("w-full aspect-square text-xs font-bold top-0")

    # Main Content
    with ui.element("div").classes(
        "grow bg-base-100 pb-4 flex flex-col overflow-hidden justify-items-center"
    ):
        with ui.grid(rows=4, columns=4).classes("w-full h-full gap-2"):
            for i in range(1, 21):  # 5x4 = 20 buttons
                ui.button(f"Button {i}").classes("w-full h-full min-h-0")


ui.run(
    dark=None,
    title="BoopBoard",
    favicon="🗿",
)
