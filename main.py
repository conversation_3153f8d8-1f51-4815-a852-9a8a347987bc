from nicegui import ui


def handle_button_action(action_name: str) -> None:
    """Handle button actions - replace with actual functionality."""
    ui.notify(f"{action_name} activated!", type="info")


with ui.element("div").classes("flex w-full h-screen"):
    # Sidebar
    with ui.element("div").classes("w-[10%] max-w-xs bg-base-200 p-4"):
        # Top icon
        with ui.element("div").classes("w-full flex justify-center"):
            ui.button(icon="settings").classes(
                "text-white bg-transparent w-full h-auto aspect-square"
            )

    # Main
    with ui.element("div").classes("grow bg-base-100 p-4"):
        with ui.grid(rows=5, columns=4).classes("grid-auto-rows-1/3"):
            for i in range(1, 25):
                ui.button(f"Button {i}").classes("w-full h-fit")


ui.run(
    dark=None,
    title="BoopBoard",
    favicon="🗿",
    viewport="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",
)
