[project]
name = "macroboard"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = ["nicegui>=2.20.0", "pydantic-settings>=2.9.1"]

[tool.ruff]
fix = true
line-length = 90
cache-dir = "/tmp/ruff_cache"

[tool.ruff.lint]
select = ["ALL"]
extend-ignore = [
    "COM812",  # missing trailing comma
    "D100",    # missing docstring in public module
    "D401",    # first line should be in imperative mood
    "TD003",   # missing TODO issue link
    "PLR0913", # too many arguments to function call
]

[tool.mypy]
plugins = ["pydantic.mypy", "pydantic_xml.mypy"]
cache_dir = "/tmp/mypy_cache"
strict = true
